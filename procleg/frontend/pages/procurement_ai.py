import streamlit as st
import procleg.frontend.utils.styles_loader as styles_loader
import base64
import uuid
import streamlit.components.v1 as components
from langchain_core.messages import HumanMessage, AIMessage
from procleg.backend.chatbot.multiagent_graph import multi_agent_graph
from procleg.backend.chatbot.conversation_thread import (
    load_conversations,
    save_conversation,
    load_conversations_metadata_only
)
import markdown
import datetime
from collections import defaultdict
from streamlit_quill import st_quill
import hashlib
import markdown2
import io
from docx import Document
from docx.shared import Pt
from bs4 import BeautifulSoup

def markdown_to_docx(md_content):
    html = markdown2.markdown(md_content)
    soup = BeautifulSoup(html, 'html.parser')

    doc = Document()
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Calibri'
    font.size = Pt(11)

    # Find all elements instead of just direct children
    # This ensures we capture all headings and paragraphs regardless of nesting
    all_elements = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'li'])

    current_list = None

    for element in all_elements:
        if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            # Reset list context when we hit a heading
            current_list = None
            level = int(element.name[1])  # Extract number from h1, h2, etc.
            heading_text = element.get_text().strip()
            doc.add_heading(heading_text, level=level)

        elif element.name == 'p':
            # Reset list context when we hit a paragraph
            current_list = None
            text = element.get_text().strip()
            if text:  # Only add non-empty paragraphs
                p = doc.add_paragraph()

                # Process the paragraph content more carefully
                # Check if the paragraph contains formatted text
                if element.find(['strong', 'b', 'em', 'i']):
                    # Handle formatted text
                    for content in element.contents:
                        if isinstance(content, str):
                            if content.strip():
                                p.add_run(content)
                        else:
                            run = p.add_run(content.get_text())
                            if content.name in ['strong', 'b']:
                                run.bold = True
                            if content.name in ['em', 'i']:
                                run.italic = True
                else:
                    # Simple text paragraph
                    p.add_run(text)

        elif element.name in ['ul', 'ol']:
            # Start a new list context
            current_list = element.name

        elif element.name == 'li' and current_list:
            # Add list item
            text = element.get_text().strip()
            if text:
                if current_list == 'ul':
                    p = doc.add_paragraph(text, style='List Bullet')
                else:  # ol
                    p = doc.add_paragraph(text, style='List Number')

    docx_io = io.BytesIO()
    doc.save(docx_io)
    docx_io.seek(0)
    return docx_io

def hash_content(text):
    return hashlib.md5(text.encode()).hexdigest()[:8]

def get_base64_encoded_image(image_path):
    with open(image_path, "rb") as img_file:
        return base64.b64encode(img_file.read()).decode()

def should_reload_conversations(prid):
    """
    Determine if conversations should be reloaded based on cache state and user changes.

    Args:
        prid (str): Current user's project ID

    Returns:
        bool: True if conversations should be reloaded
    """
    # Check if user changed
    if st.session_state.current_prid != prid:
        st.session_state.current_prid = prid
        return True

    # Check if forced reload is requested
    if st.session_state.get('reload_conversations', False):
        return True

    # Check if cache exists for this user
    cache_key = f"{prid}_procurement"
    if cache_key not in st.session_state.conversation_metadata_cache:
        return True

    # Check cache age (reload after 5 minutes)
    import time
    cache_timestamp = st.session_state.conversation_cache_timestamp.get(cache_key, 0)
    if time.time() - cache_timestamp > 300:  # 5 minutes
        return True

    return False

def load_conversations_smart(prid, force_reload=False):
    """
    Smart conversation loading that uses metadata-only loading and caching.

    Args:
        prid (str): Project ID to load conversations for
        force_reload (bool): Force reload even if cache exists

    Returns:
        list: List of conversation metadata
    """
    cache_key = f"{prid}_procurement"

    # Check if we should use cached data
    if not force_reload and not should_reload_conversations(prid):
        return st.session_state.conversation_metadata_cache[cache_key]

    try:
        # Load metadata-only for fast performance
        conversations, metrics = load_conversations_metadata_only(prid, 'procurement')

        # Update cache
        import time
        st.session_state.conversation_metadata_cache[cache_key] = conversations
        st.session_state.conversation_cache_timestamp[cache_key] = time.time()
        st.session_state.reload_conversations = False

        # Log performance for monitoring
        print(f"Loaded {len(conversations)} conversation metadata in {metrics.total_time:.2f}s "
              f"(Cache hits: {metrics.cache_hits}, S3 time: {metrics.s3_fetch_time:.2f}s)")

        return conversations

    except Exception as e:
        print(f"Error loading conversations: {e}")
        # Return empty list on error
        return []

def load_conversation_content(prid, thread_id):
    """
    Load full conversation content for a specific thread efficiently.

    Args:
        prid (str): Project ID
        thread_id (str): Thread ID to load content for

    Returns:
        dict: Full conversation data with messages
    """
    try:
        # Import the required modules for direct S3 access
        from procleg.backend.services.conversation_thread_services import ConversationThreadModel
        from procleg.backend.services import s3_services
        import json

        # Get the specific thread record from DynamoDB
        thread_record = ConversationThreadModel.get_thread_by_primary_key(thread_id, 'procurement')

        if not thread_record:
            print(f"Thread {thread_id} not found in database")
            return None

        if not thread_record.text_s3_url:
            print(f"Thread {thread_id} has no S3 URL")
            return None

        # Extract S3 key from the URL
        # URL format: https://bucket.s3.amazonaws.com/conversation_thread/filename.json
        print(f"DEBUG - S3 URL: {thread_record.text_s3_url}")

        # Handle different URL formats
        if thread_record.text_s3_url.startswith('conversation_thread/'):
            # Already in key format
            s3_key = thread_record.text_s3_url
        else:
            # Extract from full URL
            s3_url_parts = thread_record.text_s3_url.split('/')
            if len(s3_url_parts) >= 2:
                s3_key = '/'.join(s3_url_parts[-2:])  # Get last two parts: folder/filename
            else:
                print(f"ERROR - Invalid S3 URL format: {thread_record.text_s3_url}")
                return None

        print(f"DEBUG - Extracted S3 key: {s3_key}")

        # Load conversation data directly from S3
        s3_data = s3_services.read_from_s3_file(s3_key)
        if not s3_data:
            print(f"Failed to read S3 data for thread {thread_id}")
            return None

        conversation_data = json.loads(s3_data)

        if not conversation_data:
            print(f"Failed to load conversation data from S3 for thread {thread_id}")
            return None

        # Convert to the expected format
        thread_metadata = conversation_data[0] if conversation_data else {}
        messages = conversation_data[1:] if len(conversation_data) > 1 else []

        return {
            "thread_id": thread_metadata.get("thread_id", thread_id),
            "prid": prid,
            "text": [
                {
                    "role": message.get("role", ""),
                    "content": message.get("content", "")
                }
                for message in messages
            ],
            "canvas_state": thread_metadata.get("canvas_state", {}),
            "createdAt": thread_metadata.get("conversation_started_at", ""),
            "thread_name": thread_metadata.get("thread_name", "Unknown")
        }

    except Exception as e:
        print(f"Error loading conversation content for thread {thread_id}: {e}")
        return None

def render():
    """Render the ai assistant with chat interface."""
    styles_loader.load_css(page_css="procurement_ai.css")

    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []

    if "chat_active" not in st.session_state:
        st.session_state.chat_active = False

    if 'thread_id' not in st.session_state:
        st.session_state['thread_id'] = str(uuid.uuid4())

    if 'renderable_messages' not in st.session_state:
        st.session_state.renderable_messages = {}

    if 'show_canvas' not in st.session_state:
        st.session_state.show_canvas = False

    if 'editable_content' not in st.session_state:
        st.session_state.editable_content = ""

    if 'current_response' not in st.session_state:
        st.session_state.current_response = {"title": "", "content": ""}

    # Initialize conversation cache management
    if 'conversation_metadata_cache' not in st.session_state:
        st.session_state.conversation_metadata_cache = {}

    if 'conversation_cache_timestamp' not in st.session_state:
        st.session_state.conversation_cache_timestamp = {}

    if 'current_prid' not in st.session_state:
        st.session_state.current_prid = None

    if 'reload_conversations' not in st.session_state:
        st.session_state.reload_conversations = True

    if 'rfp_file_data' not in st.session_state:
        st.session_state.rfp_file_data = None

    if 'latest_render_flag' not in st.session_state:
        st.session_state.latest_render_flag = False

    config = {"configurable": {"thread_id": st.session_state['thread_id']}}
    prid = st.session_state["user"]["prid"]

    if not st.session_state.show_canvas:
        render_chat_interface(prid, config)
    else:

        col1, col2 = st.columns([1, 1])

        with col1:
            render_chat_interface(prid, config)

        with col2:
            render_editor()

def render_chat_interface(prid, config):
    """Render the chat interface without the editor."""
    aiAssistanceContainer = st.container(key="ai_assistance_container")
    with aiAssistanceContainer:
        historyColumn, chatBotColumn = st.columns([1, 4])

        # History column
        with historyColumn:
            render_history_column(prid)

        # Chatbot column
        with chatBotColumn:
            render_chat_column(prid, config)

def render_editor():
    """Render the editor interface."""
    with st.container(key="editor_header"):
        col1, col2 = st.columns([0.9, 0.1])
        with col1:
            if st.session_state.current_response:
                st.markdown("<div class='canvas-title'>Request For Proposal (live preview)</div>", unsafe_allow_html=True)
        with col2:
            if st.button("✕", key="close_canvas"):
                st.session_state.show_canvas = False
                st.rerun()
    with st.container(key="editor_container"):

        st.markdown("<div class='canvas-area'>", unsafe_allow_html=True)

        content_value = st.session_state.editable_content
        unique_key = f"quill_{hash_content(content_value)}"
        content = st_quill(value=content_value, key=unique_key, readonly=True, toolbar=[])


        if content:
            st.session_state.quill_content = content
            st.session_state.editable_content = content

        # Show download button when canvas is open and has content
        if st.session_state.show_canvas and (st.session_state.editable_content or st.session_state.rfp_file_data):
            filename = "download_ai_response.docx"

            # Determine the most current content for download
            # Priority: 1) quill_content (if user made edits), 2) editable_content (latest from LangGraph), 3) fallback
            download_content = None

            # Check if user has made manual edits in the editor
            if (hasattr(st.session_state, 'quill_content') and
                st.session_state.quill_content and
                st.session_state.quill_content != st.session_state.editable_content):
                # User has made manual edits, use those
                download_content = st.session_state.quill_content
            elif st.session_state.editable_content:
                # No manual edits, use the latest content from LangGraph
                download_content = st.session_state.editable_content
            elif hasattr(st.session_state, 'quill_content') and st.session_state.quill_content:
                # Fallback to quill_content if available
                download_content = st.session_state.quill_content
            else:
                # Final fallback - reconstruct from current RFP data if available
                if st.session_state.rfp_file_data:
                    sorted_rfp = sorted(st.session_state.rfp_file_data, key=lambda x: x['order'])
                    download_content = "\n\n".join(
                        f"{block.get('header', '')}\n\n{block.get('content', '')}"
                        for block in sorted_rfp
                    )
                else:
                    download_content = "No content available for download."

            docx_io = markdown_to_docx(download_content)

            col1, col2 = st.columns([4, 2])
            with col1:
                st.write("")
            with col2:
                st.download_button(
                    label="Download Word Document",
                    data=docx_io,
                    file_name=filename,
                    mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                )

        st.markdown("</div>", unsafe_allow_html=True)


def render_history_column(prid):
    """Render the history column with conversations."""
    historyContainer = st.container(key="history_container")
    with historyContainer:
        # Header section
        header_container = st.container()
        with header_container:
            st.markdown('<h4 style="margin-bottom: 0.5rem; margin-top: -0.8rem; font-weight: bold;">Chats</h4>', unsafe_allow_html=True)

        # New chat button
        button_container = st.container()
        with button_container:
            st.markdown('<div class="st-key-new_btn">', unsafe_allow_html=True)
            if st.button("New", key="new_chat_btn"):
                st.session_state['chat_history'] = []
                st.session_state['thread_id'] = str(uuid.uuid4())
                st.session_state['chat_active'] = False
                st.session_state.show_canvas = False
                st.session_state.editable_content = ""
                st.session_state.current_response = {"title": "", "content": ""}
                st.session_state.renderable_messages = {}
                st.session_state.rfp_file_data = None
                # No need to reload conversations for new chat - metadata doesn't change
                st.rerun()
            st.markdown('</div>', unsafe_allow_html=True)

        # Conversations section - Use smart loading with metadata-only
        conversations = load_conversations_smart(prid)

        # Sort conversations by creation date (newest first)
        conversations.sort(key=lambda x: x.get("createdAt", ""), reverse=True)

        # Group conversations by date
        convos_by_date = defaultdict(list)
        for convo in conversations:
            date_str = convo.get("createdAt", "")[:10]  # 'YYYY-MM-DD'
            try:
                date_obj = datetime.datetime.fromisoformat(date_str)
                date_label = date_obj.strftime("%b %d, %Y")
            except Exception:
                date_label = date_str or "Unknown Date"
            convos_by_date[date_label].append(convo)

        # Show most recent dates first
        for date_label in sorted(convos_by_date.keys(), reverse=True):
            with st.expander(date_label):
                # Sort conversations within each date group by creation time
                convos_by_date[date_label].sort(
                    key=lambda x: x.get("createdAt", ""),
                    reverse=True
                )
                for conversation in convos_by_date[date_label]:
                    thread_name = conversation.get("thread_name", "Untitled")
                    thread_id = conversation.get('thread_id', 'unknown')

                    # Show message count for metadata-only conversations
                    message_count = conversation.get('message_count', 0)
                    display_name = f"{thread_name} ({message_count} msgs)" if message_count > 0 else thread_name

                    if st.button(display_name, key=f"thread_{thread_id}"):
                        st.session_state['chat_history'] = []
                        st.session_state['thread_id'] = thread_id
                        st.session_state['chat_active'] = True

                        # Lazy load conversation content only when needed
                        conversation_content = load_conversation_content(prid, thread_id)
                        if conversation_content and "text" in conversation_content:
                            # Extract canvas state from conversation metadata
                            canvas_state = conversation_content.get("canvas_state", {})
                            # Load chat messages and convert to LangChain message objects for consistency
                            for message in conversation_content["text"]:
                                role = message["role"]
                                content = message["content"]
                                if role == "user":
                                    st.session_state['chat_history'].append(
                                        HumanMessage(content=content, additional_kwargs={"role": "user"})
                                    )
                                elif role == "assistant":
                                    st.session_state['chat_history'].append(
                                        AIMessage(content=content, additional_kwargs={"role": "assistant"})
                                    )
                                else:
                                    # Fallback for any other role
                                    st.session_state['chat_history'].append({'role': role, 'content': content})

                            # Check if this conversation has canvas state and restore it
                            has_canvas = canvas_state.get("has_canvas", False)

                            # If this conversation had canvas content, restore the state
                            if has_canvas and canvas_state.get("rfp_data"):
                                # Restore RFP data from saved state
                                st.session_state.rfp_file_data = canvas_state["rfp_data"]

                                # Reconstruct the editable content from stored RFP data
                                sorted_rfp = sorted(canvas_state["rfp_data"], key=lambda x: x['order'])
                                full_markdown = "\n\n".join(
                                    f"{block.get('header', '')}\n\n{block.get('content', '')}"
                                    for block in sorted_rfp
                                )
                                # Prepare content but keep canvas closed - user must click "Open" to view
                                st.session_state.editable_content = full_markdown
                                st.session_state.show_canvas = False  # Keep canvas closed by default
                                st.session_state["has_editor_response"] = True
                                st.session_state.current_response = {"title": "RFP Document", "content": full_markdown}

                                # Add "Open" button for AI messages that created/modified RFP content
                                # Use stored canvas state to determine which messages should have "Open" buttons
                                for idx, msg in enumerate(st.session_state['chat_history']):
                                    is_assistant = False
                                    msg_content = ""

                                    if hasattr(msg, 'additional_kwargs') and msg.additional_kwargs.get('role') == 'assistant':
                                        is_assistant = True
                                        msg_content = msg.content.lower()
                                    elif isinstance(msg, dict) and msg.get('role') == 'assistant':
                                        is_assistant = True
                                        msg_content = msg.get('content', '').lower()

                                    # Check if this specific AI message contains RFP-related content
                                    # Use both keyword detection and the fact that we have canvas state
                                    if is_assistant and any(keyword in msg_content for keyword in [
                                        'rfp has been created', 'rfp has been modified',
                                        'request for proposal', 'rfp template', 'rfp draft'
                                    ]):
                                        st.session_state.renderable_messages[idx] = full_markdown
                            else:
                                # Reset canvas state for conversations without RFP content
                                st.session_state.show_canvas = False
                                st.session_state.editable_content = ""
                                st.session_state.renderable_messages = {}
                                st.session_state.current_response = {"title": "", "content": ""}

                        # No need to reload conversation list - we're just switching threads
                        st.rerun()

def render_chat_column(prid, config):
    """Render the chat column with messages and input."""
    chatContainer = st.container(key="chat_container")
    with chatContainer:
        if not st.session_state.chat_active:
            st.markdown(
                """
                <div style="text-align: center; padding: 20px; font-weight: bold; font-size: 30px; color: #1c2262; margin-top: 2rem; margin-bottom: 2rem; margin-right: 4rem;">
                This is an AI assistant that supports you through your sourcing journey - from building an
                RFP to evaluating proposals and preparing supplier documents. Go ahead and give it a try!
                </div>
                """,
                unsafe_allow_html=True
            )
        else:
            # st.markdown("""
            # <div id="chat-scroll" class="custom-scrollbar" style="padding: 50px; background: #fff; height: 466px; overflow-y: auto ; overflow-x: hidden; scroll-behavior: smooth; margin-left: -4.1rem; margin-top: -3rem;">
            #     <div style="display: flex; flex-direction: column;">
            # """, unsafe_allow_html=True)

            with st.container(key="chat-scroll"):

                # Building messages inside the chat-section
                user_initials = "SG"
                if "user_name" in st.session_state:
                    parts = st.session_state["user_name"].split()
                    if len(parts) > 1:
                        user_initials = f"{parts[0][0]}{parts[1][0]}".upper()
                    else:
                        user_initials = parts[0][0].upper()

                for idx, msg in enumerate(st.session_state.chat_history):
                    role, content_raw = None, None
                    if hasattr(msg, 'additional_kwargs') and msg.additional_kwargs.get("role"):
                        role = msg.additional_kwargs.get("role")
                        content_raw = msg.content
                    elif isinstance(msg, dict) and "role" in msg and "content" in msg:
                        role = msg["role"]
                        content_raw = msg["content"]
                    else:
                        continue

                    if role == "user":
                        st.markdown(
                            f"<div class='chat-row user-row'><div class='avatar user-avatar'>{user_initials}</div><div class='chat-bubble user-bubble'>{content_raw}</div></div>",
                            unsafe_allow_html=True
                        )
                    else:
                        content = markdown.markdown(content_raw)
                        content.replace('\\', '\\\\').replace('`', '\\`').replace('"', '\\"')
                        # Render using Streamlit
                        st.markdown(f"""
                        <div class="chat-row ai-row">
                            <div class="avatar ai-avatar">AI</div>
                            <div class="chat-bubble ai-bubble" id="bubble_{idx}">
                                {content}
                                <button class="copy-btn" id="copy_btn_{idx}" title="Copy to clipboard">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)

                        # Show "Open" button below this message if it's a renderable response
                        if idx in st.session_state.renderable_messages:
                                if st.button("Open", key=f"open_canvas_{idx}"):
                                    # Use the most current RFP data if available, otherwise fall back to stored content
                                    if st.session_state.rfp_file_data:
                                        # Reconstruct from current RFP data to ensure latest changes are reflected
                                        sorted_rfp = sorted(st.session_state.rfp_file_data, key=lambda x: x['order'])
                                        current_content = "\n\n".join(
                                            f"{block.get('header', '')}\n\n{block.get('content', '')}"
                                            for block in sorted_rfp
                                        )
                                        st.session_state.editable_content = current_content
                                        # Update the stored renderable message with current content
                                        st.session_state.renderable_messages[idx] = current_content
                                        st.session_state.current_response = {"title": "RFP Document", "content": current_content}
                                    else:
                                        # Fall back to stored content if no current RFP data
                                        st.session_state.editable_content = st.session_state.renderable_messages[idx]
                                        st.session_state.current_response = {"title": "RFP Document", "content": st.session_state.renderable_messages[idx]}

                                    st.session_state.show_canvas = True
                                    st.rerun()

            if "user_message" not in st.session_state:
                st.session_state.user_message = ""

            # Input section
        input_container = st.container(key="text_area_container")
        with input_container:
                # Use a single container for the input area
            st.text_area(" ", placeholder="Ask your question here..", key="user_input", label_visibility="collapsed")
            st.button("Submit", key="send_btn", on_click=lambda: send_message(prid, config))
        components.html("""
            <script>
            function focusTextarea() {
                const textarea = parent.document.querySelector('textarea');
                if (textarea) {
                    textarea.focus();
                    textarea.addEventListener("keydown", function(event) {
                        if (event.key === "Enter" && !event.shiftKey) {
                            event.preventDefault();  // Prevent Enter from submitting
                        }
                    });
                }
            }
            // Use MutationObserver to re-apply focus on rerun
            const observer = new MutationObserver((mutationsList, observer) => {
                for (const mutation of mutationsList) {
                    if (mutation.type === 'childList') {
                        focusTextarea();
                    }
                }
            });
            observer.observe(parent.document.body, { childList: true, subtree: true });
            // Initial call
            focusTextarea();
            </script>
        """, height=0)

        components.html("""
            <script>
            (function() {
                console.log('Script loaded');

                function initialize() {
                    console.log('Initializing...');
                    const copyButtons = parent.document.querySelectorAll('.copy-btn');
                    console.log('Found copy buttons:', copyButtons.length);

                    copyButtons.forEach((button, index) => {
                        if (button.dataset.listenerAdded) return;
                        button.dataset.listenerAdded = 'true';

                        button.onclick = function(e) {
                            console.log('Copy button clicked');
                            e.preventDefault();
                            e.stopPropagation();

                            const bubble = this.closest('.ai-bubble');
                            console.log('Bubble content:', bubble.innerHTML); // Debug log

                            let content = '';
                            const walker = document.createTreeWalker(
                                bubble,
                                NodeFilter.SHOW_TEXT,
                                null,
                                false
                            );

                            let node;
                            while (node = walker.nextNode()) {
                                if (!this.contains(node)) {
                                    content += node.textContent;
                                }
                            }

                            content = content.trim();
                            console.log('Extracted content:', content); // Debug log

                            try {
                                const textarea = document.createElement('textarea');
                                textarea.value = content;
                                textarea.style.position = 'fixed';
                                textarea.style.left = '-999999px';
                                textarea.style.top = '-999999px';
                                document.body.appendChild(textarea);

                                textarea.select();
                                textarea.setSelectionRange(0, 99999); // For mobile

                                const successful = document.execCommand('copy');
                                console.log('Copy command executed:', successful);

                                document.body.removeChild(textarea);

                                // Visual feedback
                                const originalHTML = this.innerHTML;
                                this.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6L9 17L4 12"/></svg>';
                                this.style.backgroundColor = '#4CAF50';

                                setTimeout(() => {
                                    this.innerHTML = originalHTML;
                                    this.style.backgroundColor = '';
                                }, 2000);
                            } catch (err) {
                                console.error('Copy failed:', err);
                                this.style.backgroundColor = '#ff4444';
                                setTimeout(() => {
                                    this.style.backgroundColor = '';
                                }, 2000);
                            }
                        };
                    });
                }

                initialize();

                setTimeout(initialize, 1000);

                const observer = new MutationObserver(() => {
                    console.log('DOM changed, reinitializing');
                    initialize();
                });

                if (parent.document.body) {
                    observer.observe(parent.document.body, {
                        childList: true,
                        subtree: true
                    });
                }
            })();
            </script>
        """, height=0)

        # components.html("""
        #     <script>
        #     (function() {
        #         console.log('Scroll script loaded');

        #         function isChatActive() {
        #             // Check if chat is active by looking for chat messages
        #             const chatMessages = parent.document.querySelectorAll('.chat-row, .user-row, .ai-row');
        #             return chatMessages.length > 0;
        #         }

        #         function scrollToBottom() {
        #             if (!isChatActive()) {
        #                 console.log('Chat not active, skipping scroll');
        #                 return;
        #             }

        #             console.log('Chat is active, attempting to scroll');

        #             const containers = [
        #                 parent.document.querySelector('.main .block-container'),
        #                 parent.document.querySelector('[data-testid="stVerticalBlock"]'),
        #                 parent.document.querySelector('.stVerticalBlock'),
        #                 parent.document.querySelector('.st-key-chat-scroll')
        #             ].filter(Boolean);

        #             console.log('Found containers:', containers.length);

        #             containers.forEach((container, index) => {
        #                 try {
        #                     console.log(`Container ${index}:`, {
        #                         scrollHeight: container.scrollHeight,
        #                         clientHeight: container.clientHeight,
        #                         scrollTop: container.scrollTop,
        #                         offsetHeight: container.offsetHeight
        #                     });

        #                     // Force scroll
        #                     container.scrollTop = container.scrollHeight;

        #                     console.log(`After scroll ${index}:`, {
        #                         scrollTop: container.scrollTop,
        #                         scrollHeight: container.scrollHeight
        #                     });
        #                 } catch (error) {
        #                     console.error(`Error scrolling container ${index}:`, error);
        #                 }
        #             });

        #             try {
        #                 parent.window.scrollTo(0, parent.document.body.scrollHeight);
        #                 console.log('Window scroll attempted');
        #             } catch (error) {
        #                 console.error('Window scroll error:', error);
        #             }
        #         }

        #         // Function to handle conversation switching
        #         function handleConversationSwitch() {
        #             console.log('Conversation switch detected');
        #             // Wait for the new conversation to load
        #             setTimeout(() => {
        #                 if (isChatActive()) {
        #                     console.log('New conversation loaded, scrolling...');
        #                     scrollToBottom();
        #                 }
        #             }, 100);
        #         }

        #         // Observe conversation switches
        #         const conversationObserver = new MutationObserver((mutations) => {
        #             mutations.forEach((mutation) => {
        #                 if (mutation.type === 'childList') {
        #                     // Check if this is a conversation switch
        #                     const hasNewConversation = Array.from(mutation.addedNodes).some(node => {
        #                         return node.classList && (
        #                             node.classList.contains('chat-row') ||
        #                             node.classList.contains('user-row') ||
        #                             node.classList.contains('ai-row')
        #                         );
        #                     });

        #                     if (hasNewConversation) {
        #                         handleConversationSwitch();
        #                     }
        #                 }
        #             });
        #         });

        #         // Start observing the chat container
        #         const chatContainer = parent.document.querySelector('.main .block-container');
        #         if (chatContainer) {
        #             conversationObserver.observe(chatContainer, {
        #                 childList: true,
        #                 subtree: true
        #             });
        #         }

        #         // Scroll on load
        #         window.addEventListener('load', () => {
        #             console.log('Window loaded, checking chat state...');
        #             if (isChatActive()) {
        #                 console.log('Chat is active, scrolling...');
        #                 scrollToBottom();
        #             }
        #         });

        #         // Scroll on new messages
        #         const messageObserver = new MutationObserver((mutations) => {
        #             const hasNewMessages = mutations.some(mutation =>
        #                 Array.from(mutation.addedNodes).some(node =>
        #                     node.classList && (
        #                         node.classList.contains('chat-row') ||
        #                         node.classList.contains('user-row') ||
        #                         node.classList.contains('ai-row')
        #                     )
        #                 )
        #             );

        #             if (hasNewMessages) {
        #                 console.log('New message detected, scrolling...');
        #                 scrollToBottom();
        #             }
        #         });

        #         if (parent.document.body) {
        #             messageObserver.observe(parent.document.body, {
        #                 childList: true,
        #                 subtree: true
        #             });
        #         }

        #         // Check periodically for conversation changes
        #         const checkInterval = setInterval(() => {
        #             if (isChatActive()) {
        #                 console.log('Chat active, checking for changes...');
        #                 scrollToBottom();
        #             }
        #         }, 1000);

        #         // Also try scroll after short delay
        #         setTimeout(() => {
        #             console.log('Checking delayed scroll...');
        #             if (isChatActive()) {
        #                 console.log('Chat is active, performing delayed scroll...');
        #                 scrollToBottom();
        #             }
        #         }, 500);
        #     })();
        #     </script>
        # """, height=0)

        components.html("""
            <script>
            (function() {
                console.log('Scroll script loaded');

                function isChatActive() {
                    // Check if chat is active by looking for chat messages
                    const chatMessages = parent.document.querySelectorAll('.chat-row, .user-row, .ai-row');
                    return chatMessages.length > 0;
                }

                function scrollToBottom() {
                    if (!isChatActive()) {
                        console.log('Chat not active, skipping scroll');
                        return;
                    }

                    console.log('Chat is active, attempting to scroll');

                    const containers = [
                        parent.document.querySelector('.main .block-container'),
                        parent.document.querySelector('[data-testid="stVerticalBlock"]'),
                        parent.document.querySelector('.stVerticalBlock'),
                        parent.document.querySelector('.st-key-chat-scroll')
                    ].filter(Boolean); // Remove null values

                    console.log('Found containers:', containers.length);

                    containers.forEach((container, index) => {
                        try {
                            // Log container details
                            console.log(`Container ${index}:`, {
                                scrollHeight: container.scrollHeight,
                                clientHeight: container.clientHeight,
                                scrollTop: container.scrollTop,
                                offsetHeight: container.offsetHeight
                            });

                            // Force scroll
                            container.scrollTop = container.scrollHeight;

                            // Log after scroll
                            console.log(`After scroll ${index}:`, {
                                scrollTop: container.scrollTop,
                                scrollHeight: container.scrollHeight
                            });
                        } catch (error) {
                            console.error(`Error scrolling container ${index}:`, error);
                        }
                    });

                    try {
                        parent.window.scrollTo(0, parent.document.body.scrollHeight);
                        console.log('Window scroll attempted');
                    } catch (error) {
                        console.error('Window scroll error:', error);
                    }
                }

                window.addEventListener('load', () => {
                    console.log('Window loaded, checking chat state...');
                    if (isChatActive()) {
                        console.log('Chat is active, scrolling...');
                        scrollToBottom();
                    } else {
                        console.log('Chat not active on load, waiting for activation...');
                    }
                });

                const observer = new MutationObserver((mutations) => {
                    const hasNewMessages = mutations.some(mutation =>
                        Array.from(mutation.addedNodes).some(node =>
                            node.classList && (
                                node.classList.contains('chat-row') ||
                                node.classList.contains('user-row') ||
                                node.classList.contains('ai-row')
                            )
                        )
                    );

                    if (hasNewMessages) {
                        console.log('New message detected, scrolling...');
                        scrollToBottom();
                    }
                });

                if (parent.document.body) {
                    observer.observe(parent.document.body, {
                        childList: true,
                        subtree: true
                    });
                }

                const checkChatInterval = setInterval(() => {
                    if (isChatActive()) {
                        console.log('Chat became active, scrolling...');
                        scrollToBottom();
                        clearInterval(checkChatInterval); // Stop checking once chat is active
                    }
                }, 1000); // Check every second

                setTimeout(() => {
                    console.log('Checking delayed scroll...');
                    if (isChatActive()) {
                        console.log('Chat is active, performing delayed scroll...');
                        scrollToBottom();
                    } else {
                        console.log('Chat not active, skipping delayed scroll');
                    }
                }, 500);
            })();
            </script>
        """, height=0)

        def send_message(prid, config):
            if st.session_state.user_input:
                st.session_state.chat_active = True
                user_input = st.session_state.user_input

                st.session_state.chat_history.append(HumanMessage(content=user_input, additional_kwargs={"role": "user"}))

                # Prepare the latest exchange for saving (user + AI message that will be added)
                latest_exchange = []
                latest_exchange.append({'role': "user", 'content': user_input})

                try:
                    ai_response = ""
                    for event in multi_agent_graph.stream(
                        {"messages": st.session_state['chat_history'],
                         "rfp_data": st.session_state.rfp_file_data,
                         "render_md": st.session_state["latest_render_flag"]},
                        config, stream_mode="values"):
                        ai_response = event["messages"][-1].content
                        render_flag = event.get("render_md")
                        st.session_state["latest_render_flag"] = render_flag
                        rfp_file_data = event.get("rfp_data")
                        st.session_state.rfp_file_data = rfp_file_data

                    if render_flag:
                        rfp_data=rfp_file_data
                        sorted_rfp = sorted(rfp_data, key=lambda x: x['order'])
                        full_markdown = "\n\n".join(
                            f"{block.get('header', '')}\n\n{block.get('content', '')}"
                            for block in sorted_rfp
                        )

                        st.session_state.editable_content = full_markdown
                        st.session_state.show_canvas = True
                        st.session_state["has_editor_response"] = True
                        st.session_state.current_response = {"title": "RFP Document", "content": full_markdown}


                        ai_index = len(st.session_state.chat_history)
                        st.session_state.renderable_messages[ai_index] = full_markdown

                    else:
                        st.session_state.show_canvas = False

                    st.session_state.chat_history.append(
                        AIMessage(content=ai_response, additional_kwargs={"role": "assistant", "render_flag": render_flag})
                    )
                    latest_exchange.append({'role': "assistant", 'content': ai_response})

                    module = "procurement"

                    # Check if this is a new conversation (first message in thread)
                    is_new_conversation = len(st.session_state.chat_history) == 2  # user + ai message

                    # Prepare canvas state for saving
                    canvas_state = {}
                    if render_flag and st.session_state.rfp_file_data:
                        canvas_state = {
                            "has_canvas": True,
                            "rfp_data": st.session_state.rfp_file_data,
                            "editable_content": st.session_state.editable_content,
                            "show_canvas": st.session_state.show_canvas,
                            "has_editor_response": st.session_state.get("has_editor_response", False)
                        }

                    # Save only the latest exchange (current user + AI message) with canvas state
                    save_conversation(latest_exchange, prid, st.session_state['thread_id'], module, canvas_state)

                    # Only reload conversations if this is a new conversation
                    # For existing conversations, the metadata doesn't change
                    if is_new_conversation:
                        st.session_state.reload_conversations = True
                except Exception as e:
                    print(f"Error processing with LangGraph: {str(e)}")
                    st.error(f"Error getting AI response: {str(e)}")
                    # Ensure we don't reload conversations after an error
                    st.session_state.reload_conversations = False

                st.session_state.user_input = ""
